# mDNS Service

[![Component Registry](https://components.espressif.com/components/espressif/mdns/badge.svg)](https://components.espressif.com/components/espressif/mdns)

mDNS is a multicast UDP service that is used to provide local network service and host discovery.

## Examples

Get started with example test [Example](https://github.com/espressif/esp-protocols/tree/master/components/mdns/examples):

## Documentation

* View the full [documentation(English)](https://docs.espressif.com/projects/esp-protocols/mdns/docs/latest/en/index.html)
* View the full [documentation(Chinese)](https://docs.espressif.com/projects/esp-protocols/mdns/docs/latest/zh_CN/index.html)
