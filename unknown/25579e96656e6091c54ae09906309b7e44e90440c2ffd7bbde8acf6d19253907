# Setup dummy network interfaces

Note: Set two addresses so we could use one as source and another as destination
```
sudo ip link add eth2 type dummy
sudo ip addr add *************/24 dev eth2
sudo ip addr add *************/24 dev eth2
sudo ip link set eth2 up
sudo ifconfig eth2 multicast
```

# Dig on a specified interface

```
dig +short -b ************* -p 5353 @*********** myesp.local
```

or a reverse query:
```
dig +short -b ************* -p 5353 @*********** -x *************
```

# Run avahi to browse services

Ava<PERSON> needs the netif to have the "multicast" flag set

```bash
david@david-comp:~/esp/idf (feature/mdns_networking_socket)$ avahi-browse -a -r -p
+;eth2;IPv6;myesp-service2;Web Site;local
+;eth2;IPv4;myesp-service2;Web Site;local
=;eth2;IPv6;myesp-service2;Web Site;local;myesp.local;*************;80;"board=esp32" "u=user" "p=password"
=;eth2;IPv4;myesp-service2;Web Site;local;myesp.local;*************;80;"board=esp32" "u=user" "p=password"
```
