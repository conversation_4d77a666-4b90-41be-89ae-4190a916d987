menu "Example Configuration"

    orsource "$IDF_PATH/examples/common_components/env_caps/$IDF_TARGET/Kconfig.env_caps"

    config MDNS_HOSTNAME
        string "mDNS Hostname"
        default "esp32-mdns"
        help
            mDNS Hostname for example to use

    config MDNS_INSTANCE
        string "mDNS Instance Name"
        default "ESP32 with mDNS"
        help
            mDNS Instance Name for example to use

    config MDNS_PUBLISH_DELEGATE_HOST
        bool "Publish a delegated host"
        help
            Enable publishing a delegated host other than ESP32.
            The example will also add a mock service for this host.

    config MDNS_RESOLVE_TEST_SERVICES
        bool "Resolve test services"
        default n
        help
            Enable resolving test services on startup.
            These services are advertized and evaluated in automated tests.
            When executed locally, these will not be resolved and warnings appear in the log.
            Please set to false to disable initial querying to avoid warnings.

    config MDNS_ADD_MAC_TO_HOSTNAME
        bool "Add mac suffix to hostname"
        default n
        help
            If enabled, a portion of MAC address is added to the hostname, this is used
            for evaluation of tests in CI

    config MDNS_BUTTON_GPIO
        int "Button GPIO to trigger querries"
        range ENV_GPIO_RANGE_MIN ENV_GPIO_IN_RANGE_MAX
        default 0
        help
            Set the GPIO number used as mDNS test button

    config MDNS_ADD_CUSTOM_NETIF
        bool "Add user netif to mdns service"
        default n
        help
            If enabled, we try to add a custom netif to mdns service.
            Note that for using with common connection example code, we have to disable
            all predefined interfaces in mdns component setup (since we're adding one
            of the default interfaces)

endmenu
